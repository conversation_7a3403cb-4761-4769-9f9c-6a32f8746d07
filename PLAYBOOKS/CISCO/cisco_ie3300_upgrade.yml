---
- name: Upgrade Cisco IE3300 Switch Firmware
  hosts: ie3300
  gather_facts: no
  vars:
    tftp_server: "***********"
    firmware_file: "ie3x00-universalk9.17.12.04.SPA.bin"
    ansible_command_timeout: 2400
    ansible_connection: ansible.netcommon.network_cli
    ansible_network_os: ios
    ansible_user: c<PERSON><PERSON><PERSON>
    ansible_password: "Kimiwasf1rst!!"
    ansible_become: yes
    ansible_become_method: enable
    ansible_become_password: "Nhc2020c1sco"
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
    ansible_terminal_timeout: 60
    ansible_persistent_command_timeout: 60

  tasks:
    - name: Check current firmware version
      cisco.ios.ios_command:
        commands:
          - "show version | include System image"
      register: version_output

    - name: Display current version
      debug:
        msg: "Current firmware: {{ version_output.stdout[0] }}"

    - name: Check if firmware is already installed
      cisco.ios.ios_command:
        commands:
          - "show version | include {{ firmware_file }}"
      register: firmware_check
      ignore_errors: yes

    - name: Skip upgrade if firmware is already installed
      meta: end_play
      when: firmware_file in firmware_check.stdout[0]

    - name: Check flash space
      cisco.ios.ios_command:
        commands:
          - "dir flash: | include bytes free"
      register: flash_space

    - name: Extract free space
      set_fact:
        free_space: "{{ flash_space.stdout[0] | regex_search('(\\d+) bytes free') | regex_replace('(\\d+) bytes free', '\\1') | int }}"

    - name: Get firmware file size from TFTP server
      delegate_to: localhost
      shell: "ls -la /tftpboot/{{ firmware_file }} | awk '{print $5}'"
      register: file_size
      ignore_errors: yes

    - name: Set file size fact
      set_fact:
        firmware_size: "{{ file_size.stdout | int }}"
      when: file_size.stdout is defined

    - name: Verify sufficient space
      fail:
        msg: "Insufficient space in flash. Required: {{ firmware_size }}, Available: {{ free_space }}"
      when: 
        - file_size.stdout is defined
        - (firmware_size | int) > (free_space | int)
      ignore_errors: no

#    - name: Clean up old firmware files
#      cisco.ios.ios_command:
#        commands:
#          - "delete /force flash:{{ firmware_file }}"
#      ignore_errors: yes

    - name: Copy firmware to switch
      ansible.netcommon.cli_command:
        command: "copy tftp://{{ tftp_server }}/{{ firmware_file }} flash:{{ firmware_file }}"
        prompt:
          - "Destination filename \\[.*\\]\\? "
          - "Do you want to over write\\? \\[confirm\\]"
        answer:
          - "\n"
          - "\n"
      timeout: "{{ ansible_command_timeout }}"

    - name: Verify firmware file in flash
      cisco.ios.ios_command:
        commands:
          - "dir flash: | include {{ firmware_file }}"
      register: firmware_verify

    - name: Verify firmware MD5
      cisco.ios.ios_command:
        commands:
          - "verify /md5 flash:{{ firmware_file }}"
      register: md5_verify

    - name: Configure boot variable
      cisco.ios.ios_config:
        lines:
          - "no boot system"
          - "boot system flash:{{ firmware_file }}"
        save_when: always

    - name: Save configuration
      cisco.ios.ios_command:
        commands:
          - "write memory"

    - name: Reload the switch (with confirmation)
      ansible.netcommon.cli_command:
        command: "reload"
        prompt:
         - "System configuration has been modified. Save\\? \\[yes/no\\]:"
         - "Proceed with reload\\? \\[confirm\\]"
        answer:
          - "yes"
          - "\n"
      async: 1
      poll: 0
      ignore_errors: yes

    - name: Wait for switch to come back
      wait_for:
        host: "{{ inventory_hostname }}"
        port: 22
        delay: 180
        timeout: 600
      delegate_to: localhost

    - name: Verify new firmware is running
      cisco.ios.ios_command:
        commands:
          - "show version | include {{ firmware_file }}"
      register: new_firmware_check
      retries: 3
      delay: 60
      until: firmware_file in new_firmware_check.stdout[0]
      ignore_errors: yes

    - name: Report upgrade status
      debug:
        msg: "Firmware upgrade {{ 'SUCCESSFUL' if firmware_file in new_firmware_check.stdout[0] else 'FAILED' }}"
