---
- name: Identify Access Ports and Apply Authentication Configuration
  hosts: cisco_switches
  gather_facts: yes

  vars:
    access_port_authentication:
      - authentication event fail action next-method
      - authentication host-mode multi-domain
      - authentication open
      - authentication order dot1x mab
      - authentication priority dot1x mab
      - authentication port-control auto
      - authentication periodic
      - authentication violation restrict
      - mab
      - dot1x pae authenticator
      - dot1x timeout tx-period 10

  tasks:
    - name: Get Interface Information
      cisco.ios.ios_command:
        commands: show running-config interface {{ item }}
      register: interface_config
      loop: "{{ ansible_net_interfaces }}"
      when: "'GigabitEthernet' in item or 'FastEthernet' in item or 'Ethernet' in item"
      # Adjust interface types as needed for your environment

    - name: Identify Access Ports
      set_fact:
        access_ports: "{{ access_ports | default([]) + [item.item] }}"
      when: "'switchport mode access' in item.stdout"
      loop: "{{ interface_config.results }}"

    - name: Apply Authentication Configuration to Access Ports
      cisco.ios.ios_config:
        lines: "{{ access_port_authentication }}"
        parents: "interface {{ item }}"
      loop: "{{ access_ports }}"
      when: access_ports is defined and access_ports | length > 0

    - name: Save configuration
      cisco.ios.ios_command:
        commands:
          - write memory
