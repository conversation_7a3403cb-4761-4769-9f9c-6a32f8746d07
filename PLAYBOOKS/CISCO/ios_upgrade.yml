# PUSH FIRMWARE TO CISCO IOS
---
- name: Upgrade firmware on Cisco switches
  hosts: retry2960x
  gather_facts: no
  vars:
    tftp_server: "***********"
    firmware_file: "c2960x-universalk9-mz.152-7.E11.bin"
    ansible_command_timeout: 900

  tasks:
    - name: Check current firmware version
      cisco.ios.ios_command:
        commands:
          - show version | include System image file
      register: current_version

    - name: Copy firmware to switch
      ansible.netcommon.cli_command:
        command: "copy tftp://{{ tftp_server }}/{{ firmware_file }} flash:{{ firmware_file }}"
        prompt:
          - "Destination filename \\[.*\\]\\? "
        answer:
          - "\n"
      timeout: "{{ ansible_command_timeout }}"

    - name: Verify firmware file in flash
      cisco.ios.ios_command:
        commands:
          - "dir flash: | include {{ firmware_file }} "
      register: firmware_check

    - name: Change boot variable
      cisco.ios.ios_config:
        lines:
          - boot system flash:{{ firmware_file }}

    - name: Save configuration
      cisco.ios.ios_command:
        commands:
          - write memory

    - name: Reload the switch
      cisco.ios.ios_command:
        commands:
          - "reload in 1\ny" #reload in 1 minute
