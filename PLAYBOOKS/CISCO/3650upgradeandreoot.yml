---
- name: Upgrade Cisco Catalyst 3650 Switch Firmware
  hosts: 3650test
  gather_facts: no
  vars:
    tftp_server: "***********"
    firmware_file: "cat3k_caa-universalk9.16.12.12.SPA.bin"
    verify_commands:
      - "dir tftp://{{ tftp_server }}/"
      - "show flash: | include free"
    pre_copy_config:
      - "file prompt quiet"
      - "ip tftp blocksize 8192"
    test_file: "test.txt"  # The test file that worked
    ansible_command_timeout: 2400
    ansible_connection: network_cli
    ansible_network_os: ios
    ansible_user: netboss
    ansible_password: "C1$co@nhc2020"
    ansible_become: yes
    ansible_become_method: enable
    ansible_become_password: "Nhc2020c1sco"
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
    ansible_terminal_timeout: 600
    ansible_persistent_command_timeout: 600

  tasks:
    # Verify TFTP connectivity with test file first
    - name: Test TFTP connectivity with small file
      cisco.ios.ios_command:
        commands:
          - "delete /force flash:{{ test_file }}"
          - "copy tftp://{{ tftp_server }}/{{ test_file }} flash:{{ test_file }}"
      register: tftp_test
      ignore_errors: yes

    - name: Display TFTP test results
      debug:
        var: tftp_test

    - name: Fail if TFTP test unsuccessful
      fail:
        msg: "TFTP test file transfer failed. Please check TFTP server configuration."
      when: "'Error' in tftp_test.stdout[1]"

    # Clean up and prepare for firmware transfer
    - name: Delete existing firmware file if present
      cisco.ios.ios_command:
        commands:
          - "delete /force flash:{{ firmware_file }}"
      ignore_errors: yes

    # Try firmware transfer with increased timeout
    - name: Copy firmware to switch
      ansible.netcommon.cli_command:
        command: "copy tftp://{{ tftp_server }}/{{ firmware_file }} flash:"
        prompt:
          - "Destination filename"
        answer:
          - "{{ firmware_file }}"
      vars:
        ansible_command_timeout: 7200
      register: copy_result

    # Verify transfer was successful
    - name: Verify firmware in flash
      cisco.ios.ios_command:
        commands:
          - "dir flash: | include {{ firmware_file }}"
          - "verify /md5 flash:{{ firmware_file }}"
      register: verify_result
      retries: 3
      delay: 10
      until: firmware_file in verify_result.stdout[0]

    # Continue with remaining tasks only if verification succeeds
    - name: Configure boot variable
      cisco.ios.ios_config:
        lines:
          - "no boot system"
          - "boot system flash:{{ firmware_file }}"
        save_when: always
      when: firmware_file in verify_result.stdout[0]

    - name: Save configuration
      cisco.ios.ios_command:
        commands:
          - "write memory"

    - name: Reload the switch (with confirmation)
      ansible.netcommon.cli_command:
        command: "reload"
        prompt:
          - "System configuration has been modified. Save\\? \\[yes/no\\]:"
          - "Proceed with reload\\? \\[confirm\\]"
        answer:
          - "yes"
          - "\n"
      async: 1
      poll: 0
      ignore_errors: yes

    - name: Wait for switch to come back
      wait_for:
        host: "{{ inventory_hostname }}"
        port: 22
        delay: 180
        timeout: 600
      delegate_to: localhost

    - name: Verify new firmware is running
      cisco.ios.ios_command:
        commands:
          - "show version | include {{ firmware_file }}"
      register: new_firmware_check
      retries: 3
      delay: 60
      until: firmware_file in new_firmware_check.stdout[0]
      ignore_errors: yes

    - name: Report upgrade status
      debug:
        msg: "Firmware upgrade {{ 'SUCCESSFUL' if firmware_file in new_firmware_check.stdout[0] else 'FAILED' }}"
