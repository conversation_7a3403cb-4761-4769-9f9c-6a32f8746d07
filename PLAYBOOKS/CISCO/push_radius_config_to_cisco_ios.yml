---
- name: Configure RADIUS on Cisco Switch
  hosts: Murrayville
  gather_facts: no
  become: yes
  become_method: enable
  connection: network_cli
  vars:
    ansible_network_os: cisco.ios.ios
    ansible_user: cdougherty
    ansible_password: Kimiwasf1rst!!
    ansible_become_password: Nhc2020c1sco
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
    ansible_command_timeout: 60
    ansible_persistent_command_timeout: 60

  tasks:
    - name: Apply RADIUS Configuration
      cisco.ios.ios_config:
        lines:
          - aaa new-model
          - aaa group server radius ISE-RADIUS
          - server name SRVISE
          - server name SRVISE2
          - ip radius source-interface vlan119 #change vlan accordingly
          - deadtime 15
          - aaa authentication dot1x default group ISE-RADIUS
          - aaa authorization console
          - aaa authorization config-commands
          - aaa authorization network default group ISE-RADIUS
          - aaa authorization auth-proxy default group ISE-RADIUS
          - aaa accounting update newinfo periodic 1440
          - aaa accounting auth-proxy default start-stop group ISE-RADIUS
          - aaa accounting dot1x default start-stop group ISE-RADIUS
          - aaa server radius dynamic-author
          - "client ************ server-key 7 11273126472725383D2B3D327937"
          - "client ************ server-key 7 0721096F1E3C37312E131D1A552E"
          - auth-type any
          - dot1x system-auth-control
          - radius-server attribute 6 on-for-login-auth
          - radius-server attribute 8 include-in-access-req
          - radius-server attribute 25 access-request include
          - radius-server dead-criteria time 5 tries 3
          - radius-server deadtime 15
          - radius server SRVISE
          - "address ipv4 ************ auth-port 1812 acct-port 1813"
          - "key 7 11273126472725383D2B3D327937"
          - radius server SRVISE2
          - "address ipv4 ************ auth-port 1812 acct-port 1813"
          - "key 7 0721096F1E3C37312E131D1A552E"
      notify: Save Configuration

  handlers:
    - name: Save Configuration
      cisco.ios.ios_command:
        commands:
          - write memory
