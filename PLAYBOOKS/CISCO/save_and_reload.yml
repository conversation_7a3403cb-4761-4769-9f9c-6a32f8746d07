---
- name: Save and Reload Cisco Switches
  hosts: HHS
  gather_facts: no
  become: yes
  become_method: enable
  connection: network_cli
  vars:
    ansible_network_os: cisco.ios.ios
    ansible_user: cdougherty
    ansible_password: Kimiwasf1rst!!
    ansible_become_password: Nhc2020c1sco
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
    ansible_command_timeout: 180
    ansible_persistent_command_timeout: 180

  tasks:
    - name: Save configuration
      cisco.ios.ios_command:
        commands:
          - write memory

    - name: Reload device
      cisco.ios.ios_command:
        commands:
          - "reload in 1\ny" #reload in 1 minute
