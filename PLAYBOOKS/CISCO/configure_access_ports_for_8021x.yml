---
- name: Configure Access Ports
  hosts: HHStest
  gather_facts: no
  connection: network_cli
  become: yes
  become_method: enable
  vars:
    ansible_network_os: cisco.ios.ios
    ansible_user: cdougherty
    ansible_password: Kimiwasf1rst!!
    ansible_become_password: Nhc2020c1sco
    ansible_ssh_common_args: '-o StrictHostKeyChecking=no'
    ansible_command_timeout: 60
    ansible_persistent_command_timeout: 60

  tasks:
    # First ensure we're in a clean state
    - name: Reset terminal state
      cisco.ios.ios_command:
        commands:
          - command: end
            prompt: '\]#'
            answer: ''
      ignore_errors: yes

    - name: Get switchport info
      cisco.ios.ios_command:
        commands:
          - show interfaces switchport
      register: switchport_info

    - name: Find access ports
      set_fact:
        access_ports: "{{ switchport_info.stdout[0] |
                         regex_findall('Name: ([^\n]+)\nSwitchport: Enabled\n(?:[^\n]+\n)*Administrative Mode: static access') |
                         map('regex_replace', 'Name: ', '') |
                         map('regex_replace', ' ', '') |
                         list }}"

    - name: Display access ports to be configured
      debug:
        msg: "Will configure the following access ports: {{ access_ports }}"

    - name: Configure each access port using raw commands
      block:
        - name: Reset terminal state before configuration
          cisco.ios.ios_command:
            commands:
              - command: end
                prompt: '\]#'
                answer: ''
          ignore_errors: yes

        - name: Enter global configuration mode
          cisco.ios.ios_command:
            commands:
              - configure terminal
          register: config_result

        - name: Configure interface {{ item }}
          cisco.ios.ios_command:
            commands:
              - interface {{ item }}
              - authentication event fail action next-method
              - authentication host-mode multi-domain
              - authentication open
              - authentication order mab dot1x
              - authentication priority dot1x mab
              - authentication port-control auto
              - authentication periodic
              - authentication violation restrict
              - mab
              - dot1x pae authenticator
              - dot1x timeout tx-period 10
              - spanning-tree portfast edge
              - exit
          register: interface_config

        - name: Exit configuration mode
          cisco.ios.ios_command:
            commands:
              - end
          register: exit_config

        - name: Pause between configurations
          pause:
            seconds: 2
      loop: "{{ access_ports }}"
      register: config_output

    - name: Save configuration
      cisco.ios.ios_command:
        commands:
          - write memory

    - name: Final verification
      cisco.ios.ios_command:
        commands:
          - show run | include dot1x
      register: verification

    - name: Display verification results
      debug:
        msg: "{{ verification.stdout[0] }}"
