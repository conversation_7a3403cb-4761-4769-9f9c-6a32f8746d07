# UPGRADE AND RELOAD CISCO IOS
---
- name: Upgrade firmware on Cisco switches
  hosts: Murrayville
  gather_facts: no
  vars:
    tftp_server: "***********"
    firmware_file: "c2960x-universalk9-mz.152-7.E12.bin"
    ansible_command_timeout: 7200
    ansible_terminal_timeout: 600
    ansible_persistent_command_timeout: 7200

  tasks:
    # Try firmware transfer
    - name: Copy firmware to switch
      ansible.netcommon.cli_command:
        command: "copy tftp://{{ tftp_server }}/{{ firmware_file }} flash:"
        prompt:
          - "Destination filename"
        answer:
          - "{{ firmware_file }}"
      vars:
        ansible_command_timeout: 7200
      register: copy_result

    # Verify transfer
    - name: Verify firmware in flash
      cisco.ios.ios_command:
        commands:
          - "dir flash: | include {{ firmware_file }}"
      register: verify_result

    - name: Change boot variable
      cisco.ios.ios_config:
        lines:
          - boot system flash:{{ firmware_file }}
      
    - name: Save configuration
      cisco.ios.ios_command:
        commands:
          - write memory

    - name: reload device
      ios_command:
        commands:
          - "reload in 1\ny" #reload in 1 minute
